<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Explore More Near Nuwara Eliya</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: #0f2027; /* fallback for old browsers */
            background: -webkit-linear-gradient(to right, #38761d, #203A43, #38761d); /* Chrome 10-25, Safari 5.1-6 */
            background: linear-gradient(to right, #38761d, #203A43, #38761d); /* W3C, IE 10+/ Edge, Firefox 16+, Chrome 26+, Opera 12+, Safari 7+ */
            color: #f0f0f0;
        }
        .header {
            text-align: center;
            padding: 60px 20px;
            background: rgba(0,0,0,0.3);
        }
        .header h1 {
            font-size: 3.5em;
            margin: 0;
            font-weight: 300;
            letter-spacing: 2px;
        }
        .header p {
            font-size: 1.2em;
            color: #a7c4bc;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr;
            gap: 30px;
            padding: 40px;
            max-width: 1400px;
            margin: 0 auto;
        }

        @media (min-width: 768px) {
            .container {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 1200px) {
            .container {
                grid-template-columns: repeat(3, 1fr);
            }
        }
        .card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.37);
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.18);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .card:hover {
            transform: translateY(-10px);
            box-shadow: 0 16px 40px 0 rgba(0, 0, 0, 0.5);
        }
        .card img {
            width: 100%;
            height: 220px;
            object-fit: cover;
        }
        .card-content {
            padding: 25px;
        }
        .card-content h3 {
            font-size: 1.8em;
            margin-top: 0;
            color: #7fb069;
        }
        .card-content p {
            font-size: 1em;
            line-height: 1.6;
            color: #c5c5c5;
        }
        .images-gallery {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 8px;
            margin-top: 25px;
            border-radius: 10px;
            overflow: hidden;
        }
        .gallery-image {
            width: 100%;
            height: 100px;
            object-fit: cover;
            cursor: pointer;
            transition: all 0.4s ease;
            position: relative;
        }
        .gallery-image:hover {
            transform: scale(1.08);
            z-index: 2;
            border-radius: 5px;
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(26, 58, 46, 0.95);
            backdrop-filter: blur(8px);
            -webkit-backdrop-filter: blur(8px);
        }
        .modal-image {
            margin: auto;
            display: block;
            max-width: 85%;
            max-height: 85%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 10px;
        }
        .close {
            position: absolute;
            top: 25px;
            right: 45px;
            color: white;
            font-size: 50px;
            font-weight: 300;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .close:hover {
            color: #bbb;
            transform: scale(1.1);
        }

        .footer {
            text-align: center;
            padding: 20px;
            margin-top: 40px;
            background: rgba(0,0,0,0.2);
            font-size: 0.9em;
        }
    </style>
</head>
<body>

    <div class="header">
        <h1>More to Explore</h1>
        <p>Discover the breathtaking landscapes and hidden gems around Nuwara Eliya.</p>
    </div>

    <div class="container">
        <!-- Card 1: Gregory Lake -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/gregory-lake/ug-lk-photowalk-2018-03-25-lake-gregory-1.jpg" alt="Gregory Lake">
            <div class="card-content">
                <h3>Gregory Lake</h3>
                <p>A prominent attraction built in 1873, perfect for water sports, recreational activities, and relaxing strolls. Enjoy speed boats, swan boats, pony rides, and a scenic bicycle path around the lake.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/gregory-lake/ug-lk-photowalk-2018-03-25-lake-gregory-1.jpg" alt="Gregory Lake" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/gregory-lake/images-2.jpg" alt="Gregory Lake" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/gregory-lake/img-20191229-wa0123-largejpg.jpg" alt="Gregory Lake" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

        <!-- Card 2: Victoria Park -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/victoria-park/lk94009647-03-e.JPG" alt="Victoria Park">
            <div class="card-content">
                <h3>Victoria Park</h3>
                <p>Named after Queen Victoria, this 27-acre park is a beautifully maintained garden with colorful flora. It's a perfect spot for holidaymakers, especially when the flowers are in full bloom from March to May.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/victoria-park/lk94009647-03-e.JPG" alt="Victoria Park" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/victoria-park/victoria-park.jpg" alt="Victoria Park" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/victoria-park/nuwara-eliya-victoria-park.webp" alt="Victoria Park" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

        <!-- Card 3: Galway’s Land National Park -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/galways-land-national-park/horton-plains-national-park-sri-lanka-7761c.jpg" alt="Galway’s Land National Park">
            <div class="card-content">
                <h3>Galway’s Land National Park</h3>
                <p>The only national park within Nuwara Eliya city limits, it hosts a unique montane ecosystem. It's considered one of the most important birding areas in Sri Lanka, rich with native and foreign floral varieties.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/galways-land-national-park/horton-plains-national-park-sri-lanka-7761c.jpg" alt="Galway’s Land National Park" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/galways-land-national-park/images.jpg" alt="Galway’s Land National Park" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/galways-land-national-park/image1.jpg" alt="Galway’s Land National Park" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

        <!-- Card 4: Single Tree Hill -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/single-tree-hill/images.jpg" alt="Single Tree Hill">
            <div class="card-content">
                <h3>Single Tree Hill</h3>
                <p>As the 7th highest mountain in Sri Lanka, it offers an outstanding sunrise view and a panoramic vista of Nuwara Eliya. The 90-minute trek through tea plantations is ideal for bird watchers and nature lovers.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/single-tree-hill/images.jpg" alt="Single Tree Hill" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/single-tree-hill/6e.jpg" alt="Single Tree Hill" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/single-tree-hill/images2.jpg" alt="Single Tree Hill" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

        <!-- Card 5: Aberdeen Waterfalls -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/aberdeen-waterfalls/images2.jpg" alt="Aberdeen Waterfalls">
            <div class="card-content">
                <h3>Aberdeen Waterfalls</h3>
                <p>One of the most iconic waterfalls in the district. Reaching it involves a scenic and therapeutic hike through lush undergrowth. The magnificent cascade at the end of the trail is a truly stunning sight.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/aberdeen-waterfalls/images2.jpg" alt="Aberdeen Waterfalls" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/aberdeen-waterfalls/visit-srilanka.jpg" alt="Aberdeen Waterfalls" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/aberdeen-waterfalls/aberdeen5-1024x768.jpeg" alt="Aberdeen Waterfalls" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

        <!-- Card 6: Laxapana Waterfalls -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/laxapana-waterfalls/beautiful-waterfall-in.jpg" alt="Laxapana Waterfalls">
            <div class="card-content">
                <h3>Laxapana Waterfalls</h3>
                <p>Known for its stunning beauty and impressive flow, Laxapana is another of the area's majestic waterfalls. It's a fantastic destination for photographers and anyone looking to witness the raw power of nature.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/laxapana-waterfalls/beautiful-waterfall-in.jpg" alt="Laxapana Waterfalls" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/laxapana-waterfalls/images3.jpg" alt="Laxapana Waterfalls" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/laxapana-waterfalls/images.jpg" alt="Laxapana Waterfalls" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

        <!-- Card 7: World's End -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/worlds-end/images.jpg" alt="World's End">
            <div class="card-content">
                <h3>World's End</h3>
                <p>Stand at the edge of eternity where dramatic cliffs plunge into misty valleys. This iconic precipice in Horton Plains offers breathtaking panoramic views that stretch to the southern coast on clear days.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/worlds-end/images.jpg" alt="World's End" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/worlds-end/imagess.jpg" alt="World's End" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/worlds-end/sl-horton-plains-np-asv2020-01-img15.jpg" alt="World's End" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

        <!-- Card 8: Pattipola Station -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/pattipola-station/pattipola-railway-station.jpg" alt="Pattipola Railway Station">
            <div class="card-content">
                <h3>Pattipola Station</h3>
                <p>Journey to Sri Lanka's highest railway station, perched at 1,898 meters above sea level. Surrounded by emerald tea plantations and rolling hills, this remote station offers a glimpse into authentic highland life.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/pattipola-station/pattipola-railway-station.jpg" alt="Pattipola Railway Station" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/pattipola-station/504506697-3127330374113900-1634256031504468854-n.jpg" alt="Pattipola Station Platform" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/pattipola-station/pattipola-station-is-the-highest-14848.jpg" alt="Pattipola Station Sign" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>

        <!-- Card 9: Hakgala Gardens -->
        <div class="card">
            <img src="https://delwest.lk/storage/explore/hakgala-gardens/imagesssss.jpg" alt="Hakgala Gardens">
            <div class="card-content">
                <h3>Hakgala Gardens</h3>
                <p>Immerse yourself in a botanical paradise where rare endemic species flourish in the cool mountain climate. This high-altitude garden showcases the incredible biodiversity of Sri Lanka's hill country.</p>
                <div class="images-gallery">
                    <img src="https://delwest.lk/storage/explore/hakgala-gardens/imagesssss.jpg" alt="Hakgala Gardens" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/hakgala-gardens/imssage.jpg" alt="Hakgala Gardens" class="gallery-image" onclick="openModal(this)">
                    <img src="https://delwest.lk/storage/explore/hakgala-gardens/hakgala-botanical-gardens-title-photo-orig.jpg" alt="Hakgala Gardens" class="gallery-image" onclick="openModal(this)">
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <p>Happy Exploring!</p>
    </div>

    <div id="imageModal" class="modal">
        <span class="close" onclick="closeModal()">&times;</span>
        <img class="modal-image" id="modalImage">
    </div>

    <script>
        const modal = document.getElementById('imageModal');
        const modalImg = document.getElementById('modalImage');

        function openModal(img) {
            modal.style.display = 'block';
            modalImg.src = img.src;
            document.body.style.overflow = 'hidden';
        }

        function closeModal() {
            modal.style.display = 'none';
            document.body.style.overflow = 'auto';
        }

        modal.onclick = function(event) {
            if (event.target === modal) {
                closeModal();
            }
        }
        
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        });
    </script>
</body>
</html>